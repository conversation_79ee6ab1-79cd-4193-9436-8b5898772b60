// Each #kernel tells which function to compile; you can have many kernels
#pragma kernel CSMain

struct SourceVertex
{
    float3 positionOS;
    float2 uv;
    float2 sourceUV;
};

struct DrawVertex
{
    float3 positionWS;
    float2 uv;
    float2 sourceUV;
};

struct DrawTriangle
{
    DrawVertex verticles[3];
};

struct indirectArgs
{
    uint numVerticlesPerInstance;
    uint numInstance;
    uint startVertexIndex;
    uint startInstanceIndex;
};

StructuredBuffer<SourceVertex> _SourceVerticles;
AppendStructuredBuffer<DrawTriangle> _DrawTriangles;
RWStructuredBuffer<indirectArgs> _IndirectArgsBuffer;

int _NumSourceTriangles;

float _Height;
float _HeightRandom;

float _Width;
float _WidthRandom;

float _Forward;
float _ForwardRandom;

float4x4 _LocalToWorld;

float rand(float3 co)
{
    return frac(sin(dot(co.xyz, float3(12.9898, 78.233, 53.539))) * 43758.5453);
}

DrawVertex TransformToWorldSpace(float3 pos,float2 uv,float2 sourceUV)
{
    DrawVertex o;
    o.positionWS = mul(_LocalToWorld,float4(pos,1)).xyz;
    o.uv = uv;
    o.sourceUV = sourceUV;
    return o;
}

void SetupAndOutputTriangle(DrawVertex a,DrawVertex b,DrawVertex c)
{
    DrawTriangle tri;
    
    tri.verticles[0] = a;
    tri.verticles[1] = b;
    tri.verticles[2] = c;

    _DrawTriangles.Append(tri);
}

[numthreads(128,1,1)]
void CSMain (uint3 id : SV_DispatchThreadID)
{
    int triStart = (int)id;
    if(triStart >= _NumSourceTriangles)
    {
        return;
    }
    
    DrawVertex input[3];
    SourceVertex sv = _SourceVerticles[triStart];
    float3 pos = sv.positionOS;
    float2 sourceUV = sv.sourceUV;

    float height = (rand(pos) * 2 - 1) * _HeightRandom + _Height;
    float width = (rand(pos) * 2 - 1) * _WidthRandom + _Width;
    float forward = (rand(pos) * 2 - 1) * _ForwardRandom + _Forward;

    input[0] = TransformToWorldSpace(pos + float3(0, height, 0),float2(0.5,1),sourceUV);
    input[1] = TransformToWorldSpace(pos + float3(width,0,forward),float2(0,0),sourceUV);
    input[2] = TransformToWorldSpace(pos + float3(-width,0,-forward),float2(1,0),sourceUV);

    SetupAndOutputTriangle(input[0],input[1],input[2]);

    InterlockedAdd(_IndirectArgsBuffer[0].numVerticlesPerInstance,3);
}
